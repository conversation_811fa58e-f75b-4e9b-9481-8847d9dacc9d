"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { MessageSquare, FileText, Mic, Loader, Info, Book, X, ChevronLeft, ChevronRight, MessageCircle } from "lucide-react"
import { CompactThemeToggle } from "../ThemeToggle"
import ChatTab from "./ChatTab"
import { getFirestore, collection, query, where, getDocs, addDoc, serverTimestamp, orderBy, limit, doc, getDoc } from "firebase/firestore"
import { useGetNamespace } from "./useGetNamespace"
import { useSession } from "next-auth/react"
import { v4 as uuidv4 } from "uuid"
import useUpload, { StatusText } from "./useUpload"
import { useConversation } from "@11labs/react"
import { db } from "components/firebase"
import SideBar from "./SideBar"
import Rehearsals from "./Rehearsals"
import FileDetails from "./FileDetails"
import ScriptTab from "./ScriptTab"
import ResponseTab from "./ResponseTab"
import { updateAgentVoice } from "./elevenlabs"

interface ScriptFile {
  id: string
  name: string
  namespace: string
}

interface ChatMessage {
  id?: string
  tempId?: string
  role: "user" | "assistant"
  content: string
  timestamp: string
  audioUrl?: string
  fileDocumentId?: string
}

interface ReadermodalProps {
  isOpen: boolean
  onClose: () => void
  fileId?: string
}

function Readermodal({ isOpen, onClose, fileId }: ReadermodalProps) {
  const [activeTab, setActiveTab] = useState<string | null>(null)
  const [activeSection, setActiveSection] = useState<string>("rehearsing")
  const [scriptFiles, setScriptFiles] = useState<ScriptFile[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isUploading, setIsUploading] = useState<boolean>(false)
  const [uploadProgress, setUploadProgress] = useState<number | null>(null)
  const [uploadStatusText, setUploadStatusText] = useState<string>("Uploading script...")
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [chatId, setChatId] = useState<string | null>(null)
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([])
  const [fileDocumentId, setFileDocumentId] = useState<string | null>(null)
  const [selectedFileNamespace, setSelectedFileNamespace] = useState<string | null>(null)
  const [fileName, setFileName] = useState<string | null>(null)
  const isMounted = useRef(true)
  const messagesProcessingRef = useRef(false)
  const [hasPermission, setHasPermission] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [isListening, setIsListening] = useState(false)
  const [voiceErrorMessage, setVoiceErrorMessage] = useState("")
  const [apiConfigStatus, setApiConfigStatus] = useState<'unchecked' | 'valid' | 'invalid' | 'connecting'>('unchecked')
  const [detailedErrorInfo, setDetailedErrorInfo] = useState<string | null>(null)
  const { data: session, status: sessionStatus } = useSession()
  const userId = session?.user?.email || ""
  const { handleUpload, progress, status, error: uploadError } = useUpload()
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false)

  // Script-related state variables
  const [scriptContent, setScriptContent] = useState<string>("")
  const [isScriptLoading, setIsScriptLoading] = useState<boolean>(false)
  const [isScriptReady, setIsScriptReady] = useState<boolean>(false)
  const [isFormatting, setIsFormatting] = useState<boolean>(false)
  const [formattedMarkdown, setFormattedMarkdown] = useState<string>("")

  // Voice selection state
  const [selectedVoiceId, setSelectedVoiceId] = useState<string | null>(null)
  const [isUpdatingVoice, setIsUpdatingVoice] = useState(false)

  // Response tab state for conversation logging
  const [conversationMessages, setConversationMessages] = useState<Array<{
    id: string;
    type: "user" | "assistant";
    content: string;
    timestamp: Date;
  }>>([])
  const [sessionStartTime, setSessionStartTime] = useState<Date | null>(null)
  const [sessionDuration, setSessionDuration] = useState(0)

  // Global recording state for Self Take functionality
  const [isRecording, setIsRecording] = useState(false)
  const [recordingError, setRecordingError] = useState<string | null>(null)
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const [recordings, setRecordings] = useState<Array<{
    id: string;
    filename: string;
    url: string;
    timestamp: Date;
    rehearsalId: string;
  }>>([])
  const [playingRecording, setPlayingRecording] = useState<string | null>(null)
  const [audioElements, setAudioElements] = useState<Record<string, HTMLAudioElement>>({})

  const conversation = useConversation({
    apiKey: process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || '***************************************************',
    onConnect: () => {
      console.log("Connected to ElevenLabs API successfully")
      setApiConfigStatus('valid')
      setDetailedErrorInfo(null)
      setSessionStartTime(new Date())
      setConversationMessages([]) // Clear previous messages
    },
    onDisconnect: () => {
      console.log("Disconnected from ElevenLabs")
      setIsListening(false)
      setSessionStartTime(null)
      setSessionDuration(0)
      setConversationMessages([]) // Clear messages on disconnect
      if (apiConfigStatus === 'valid') {
        setApiConfigStatus('unchecked')
      }
    },
    onError: (error: unknown) => {
      console.error("ElevenLabs API error:", error)
      const errorMessage = error instanceof Error ? error.message : String(error)
      setDetailedErrorInfo(errorMessage)
      setApiConfigStatus('invalid')
      if (isListening) {
        setIsListening(false)
        setVoiceErrorMessage("Connection error: " + errorMessage.substring(0, 100))
      }
    },
    onMessage: (message) => {
      if (typeof message === "string") {
        console.log("[AUDIO] Voice input received:", message)
        // Add user message to conversation log
        const userMessage = {
          id: uuidv4(),
          type: "user" as const,
          content: message,
          timestamp: new Date()
        }
        setConversationMessages(prev => [...prev, userMessage])
      } else if (message && typeof message === "object" && 'message' in message) {
        console.log("[AUDIO] AI response received:", message.message)
        // Add assistant message to conversation log
        const assistantMessage = {
          id: uuidv4(),
          type: "assistant" as const,
          content: message.message,
          timestamp: new Date()
        }
        setConversationMessages(prev => [...prev, assistantMessage])
      }
    },
    onModeChange: (mode: any) => {
      console.log("[AUDIO] Mode changed to:", mode)
    },
    onStatusChange: (status: any) => {
      console.log("[AUDIO] Status changed to:", status)
    },
    // Enable audio output
    onAudioPlay: () => {
      console.log("[AUDIO] Audio playback started")
    },
    onAudioStop: () => {
      console.log("[AUDIO] Audio playback stopped")
    }
  })

  const { status: voiceStatus, isSpeaking } = conversation

  // Debug function to test voice selection flow
  const debugVoiceSelection = async (testVoiceId: string) => {
    console.log("[DEBUG] Testing voice selection flow with voiceId:", testVoiceId)

    const agentId = process.env.NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID || '1WU4LPk9482VXQFb80aq'
    const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || '***************************************************'

    console.log("[DEBUG] Configuration:", {
      agentId,
      hasApiKey: !!apiKey,
      testVoiceId,
      currentSelectedVoice: selectedVoiceId
    })

    try {
      // Test the updateAgentVoice function directly
      const result = await updateAgentVoice(agentId, testVoiceId, apiKey)
      console.log("[DEBUG] Direct updateAgentVoice call successful:", result)
      return true
    } catch (error) {
      console.error("[DEBUG] Direct updateAgentVoice call failed:", error)
      return false
    }
  }

  // Make debug function available globally for testing
  useEffect(() => {
    if (typeof window !== 'undefined') {
      (window as any).debugVoiceSelection = debugVoiceSelection
    }
  }, [debugVoiceSelection])

  useEffect(() => {
    console.log("ElevenLabs config check:", {
      agentIdAvailable: !!process.env.NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID,
      apiKeyAvailable: !!process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY,
      agentId: process.env.NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID,
    })
    if (isOpen) {
      setApiConfigStatus('unchecked')
    }
  }, [isOpen])

  useEffect(() => {
    if (fileId) {
      setActiveTab(fileId)
    }
  }, [fileId])

  // Track session duration
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null

    if (sessionStartTime && isListening) {
      interval = setInterval(() => {
        const now = new Date()
        const duration = Math.floor((now.getTime() - sessionStartTime.getTime()) / 1000)
        setSessionDuration(duration)
      }, 1000)
    }

    return () => {
      if (interval) {
        clearInterval(interval)
      }
    }
  }, [sessionStartTime, isListening])

  const { namespace, fileName: namespaceFileName } = useGetNamespace(userId, activeTab || null)

  useEffect(() => {
    if (namespaceFileName) {
      setFileName(namespaceFileName)
    }
  }, [namespaceFileName])

  useEffect(() => {
    if (status === StatusText.ERROR && uploadError) {
      setError(`Upload error: ${uploadError}`)
      setIsUploading(false)
      setUploadProgress(null)
    } else if (status === StatusText.UPLOADING) {
      setIsUploading(true)
      setUploadProgress(progress || 0)
      setUploadStatusText("Uploading script...")
    } else if (status === StatusText.PROCESSING) {
      setIsUploading(true)
      setUploadProgress(progress || 0)
      setUploadStatusText("Processing script...")
    } else if (status === StatusText.COMPLETED) {
      setIsUploading(false)
      setUploadProgress(null)
      fetchScriptFiles()
    }
  }, [status, progress, uploadError])

  // Audio debugging function
  const debugAudioSettings = async () => {
    console.log("[AUDIO_DEBUG] Checking browser audio capabilities...")

    try {
      // Check if audio context is available
      const AudioContext = window.AudioContext || (window as any).webkitAudioContext
      if (!AudioContext) {
        console.error("[AUDIO_DEBUG] AudioContext not supported")
        return false
      }

      // Check audio output devices
      if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
        const devices = await navigator.mediaDevices.enumerateDevices()
        const audioOutputs = devices.filter(device => device.kind === 'audiooutput')
        const audioInputs = devices.filter(device => device.kind === 'audioinput')

        console.log("[AUDIO_DEBUG] Audio devices:", {
          outputs: audioOutputs.length,
          inputs: audioInputs.length,
          outputDevices: audioOutputs.map(d => ({ id: d.deviceId, label: d.label })),
          inputDevices: audioInputs.map(d => ({ id: d.deviceId, label: d.label }))
        })
      }

      // Check volume settings
      console.log("[AUDIO_DEBUG] Current audio state:", {
        isMuted: isMuted,
        conversationStatus: voiceStatus,
        isSpeaking: isSpeaking,
        isListening: isListening
      })

      return true
    } catch (error) {
      console.error("[AUDIO_DEBUG] Audio debug failed:", error)
      return false
    }
  }

  useEffect(() => {
    const requestMicPermission = async () => {
      try {
        console.log("[AUDIO] Requesting microphone permission...")
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
        setHasPermission(true)
        setVoiceErrorMessage("")
        console.log("[AUDIO] Microphone permission granted")

        // Stop the stream since we just needed permission
        stream.getTracks().forEach(track => track.stop())

        // Debug audio settings
        await debugAudioSettings()

      } catch (error) {
        setVoiceErrorMessage("Microphone access denied - please enable in browser settings")
        console.error("[AUDIO] Error accessing microphone:", error)
      }
    }

    if (activeSection === "rehearsing") {
      requestMicPermission()
    }
  }, [activeSection])

  // Make debug functions available globally
  useEffect(() => {
    if (typeof window !== 'undefined') {
      (window as any).debugAudioSettings = debugAudioSettings
    }
  }, [])

  const handleVoiceSelect = async (voiceId: string) => {
    console.log(`[VOICE_SELECT] Starting voice selection process for voiceId: ${voiceId}`)

    try {
      // Validate voiceId parameter
      if (!voiceId || typeof voiceId !== 'string') {
        throw new Error("Invalid voice ID provided")
      }

      setSelectedVoiceId(voiceId)
      setIsUpdatingVoice(true)
      setVoiceErrorMessage("") // Clear any previous errors

      // Get and validate agent ID
      const agentId = process.env.NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID || '1WU4LPk9482VXQFb80aq'
      console.log(`[VOICE_SELECT] Using agent ID: ${agentId}`)

      if (!agentId) {
        throw new Error("Missing ElevenLabs agent ID configuration")
      }

      // Get API key for the update
      const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || '***************************************************'
      console.log(`[VOICE_SELECT] API key available: ${!!apiKey}`)

      if (!apiKey) {
        throw new Error("Missing ElevenLabs API key configuration")
      }

      console.log(`[VOICE_SELECT] Updating agent ${agentId} voice to ${voiceId}`)
      setVoiceErrorMessage("Updating voice configuration...")

      // Update the agent's voice configuration with explicit API key
      const updateResult = await updateAgentVoice(agentId, voiceId, apiKey)
      console.log(`[VOICE_SELECT] Successfully updated agent voice:`, updateResult)

      // Provide user feedback
      setVoiceErrorMessage("Voice updated successfully!")
      setTimeout(() => {
        setVoiceErrorMessage("")
      }, 2000)

      // If there's an active conversation, restart it with the new voice
      if (isListening && voiceStatus === 'connected') {
        console.log('[VOICE_SELECT] Restarting conversation with new voice...')
        setVoiceErrorMessage("Switching to new voice...")

        try {
          await handleEndConversation()
          // Small delay to ensure clean disconnection
          setTimeout(() => {
            handleStartConversation()
          }, 1500)
        } catch (restartError) {
          console.error('[VOICE_SELECT] Error restarting conversation:', restartError)
          setVoiceErrorMessage("Voice updated, but conversation restart failed. Please restart manually.")
        }
      }

    } catch (error) {
      console.error('[VOICE_SELECT] Error updating agent voice:', error)

      // Provide detailed error information
      const errorMessage = error instanceof Error ? error.message : String(error)
      let userFriendlyMessage = "Failed to update voice configuration"

      if (errorMessage.includes("API key") || errorMessage.includes("auth")) {
        userFriendlyMessage = "Authentication failed - please check API configuration"
      } else if (errorMessage.includes("agent") || errorMessage.includes("Agent")) {
        userFriendlyMessage = "Invalid agent configuration - please check agent ID"
      } else if (errorMessage.includes("voice") || errorMessage.includes("Voice")) {
        userFriendlyMessage = "Invalid voice selection - please try a different voice"
      } else if (errorMessage.includes("network") || errorMessage.includes("fetch")) {
        userFriendlyMessage = "Network error - please check your connection"
      }

      setVoiceErrorMessage(userFriendlyMessage)
      setDetailedErrorInfo(errorMessage)

      // Clear the error after a longer period for errors
      setTimeout(() => {
        setVoiceErrorMessage("")
        setDetailedErrorInfo(null)
      }, 5000)
    } finally {
      setIsUpdatingVoice(false)
    }
  }

  // Global Self Take Recording Functions
  const startSelfTakeRecording = async () => {
    if (!userId) {
      setRecordingError('Please sign in to record');
      return;
    }

    try {
      setRecordingError(null);
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const recorder = new MediaRecorder(stream);
      mediaRecorderRef.current = recorder;

      const chunks: Blob[] = [];
      recorder.ondataavailable = (e) => chunks.push(e.data);
      recorder.onstop = () => {
        const blob = new Blob(chunks, { type: "audio/mp3" });
        uploadSelfTakeRecording(blob);
        stream.getTracks().forEach((track) => track.stop());
      };

      recorder.start();
      setIsRecording(true);
    } catch (error) {
      setRecordingError("Failed to start recording. Please check your microphone permissions.");
      console.error('Recording error:', error);
    }
  };

  const stopSelfTakeRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  const uploadSelfTakeRecording = async (blob: Blob) => {
    if (!userId) return;

    try {
      const formData = new FormData();
      const rehearsalId = `rehearsal-${Date.now()}`;
      const filename = `selftake-${rehearsalId}.mp3`;

      formData.append("audio", blob, filename);
      formData.append("userId", userId);
      formData.append("rehearsalId", rehearsalId);
      formData.append("scriptName", fileName || "Unknown Script");

      const response = await fetch("/api/selfTakeAudio", {
        method: "POST",
        body: formData,
      });

      if (response.ok) {
        const data = await response.json();
        const newRecording = {
          id: data.id,
          filename: data.filename,
          url: data.url,
          timestamp: new Date(data.timestamp),
          rehearsalId: rehearsalId
        };

        setRecordings(prev => [newRecording, ...prev]);
        setRecordingError(null);
      } else {
        const errorData = await response.json();
        setRecordingError(errorData.error || 'Failed to save recording');
      }
    } catch (error) {
      console.error('Upload error:', error);
      setRecordingError('Failed to upload recording');
    }
  };

  const toggleRecordingPlayback = (recording: any) => {
    const audioKey = recording.id;

    if (playingRecording === audioKey) {
      // Stop current playback
      if (audioElements[audioKey]) {
        audioElements[audioKey].pause();
        audioElements[audioKey].currentTime = 0;
      }
      setPlayingRecording(null);
    } else {
      // Stop any other playing audio
      Object.values(audioElements).forEach(audio => {
        audio.pause();
        audio.currentTime = 0;
      });
      setPlayingRecording(null);

      // Start new playback
      if (!audioElements[audioKey]) {
        const audio = new Audio(recording.url);
        audio.onended = () => setPlayingRecording(null);
        audio.onerror = () => {
          setRecordingError('Failed to play recording');
          setPlayingRecording(null);
        };

        setAudioElements(prev => ({ ...prev, [audioKey]: audio }));
        audio.play().then(() => setPlayingRecording(audioKey));
      } else {
        audioElements[audioKey].currentTime = 0;
        audioElements[audioKey].play().then(() => setPlayingRecording(audioKey));
      }
    }
  };

  const deleteRecording = async (recording: any) => {
    if (!userId) return;

    if (!confirm(`Delete recording from ${recording.timestamp.toLocaleString()}?`)) {
      return;
    }

    try {
      const response = await fetch("/api/selfTakeAudio", {
        method: "DELETE",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          userId,
          recordingId: recording.id,
          filename: recording.filename
        }),
      });

      if (response.ok) {
        setRecordings(prev => prev.filter(r => r.id !== recording.id));

        // Stop and clean up audio if it's playing
        if (playingRecording === recording.id) {
          if (audioElements[recording.id]) {
            audioElements[recording.id].pause();
          }
          setPlayingRecording(null);
        }

        // Remove audio element
        setAudioElements(prev => {
          const newElements = { ...prev };
          delete newElements[recording.id];
          return newElements;
        });
      } else {
        const errorData = await response.json();
        setRecordingError(errorData.error || 'Failed to delete recording');
      }
    } catch (error) {
      console.error('Delete error:', error);
      setRecordingError('Failed to delete recording');
    }
  };

  const handleStartConversation = async () => {
    console.log("[CONVERSATION_START] Initiating conversation start process")

    try {
      setVoiceErrorMessage("")
      setDetailedErrorInfo(null)

      // Validate user authentication
      if (!session?.user?.email) {
        throw new Error("You must be signed in to use voice features")
      }

      // Validate voice selection
      if (!selectedVoiceId) {
        setVoiceErrorMessage("Please select a voice before starting the conversation")
        return
      }

      // Validate agent configuration
      const agentId = process.env.NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID || '1WU4LPk9482VXQFb80aq'
      if (!agentId) {
        throw new Error("Missing ElevenLabs agent ID configuration")
      }

      // Validate API key
      const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || '***************************************************'
      if (!apiKey) {
        throw new Error("Missing ElevenLabs API key configuration")
      }

      console.log("[CONVERSATION_START] Configuration validated:", {
        agentId: agentId,
        voiceId: selectedVoiceId,
        scriptId: activeTab,
        scriptName: fileName,
        hasApiKey: !!apiKey
      })

      setApiConfigStatus('connecting')
      setIsListening(true)
      setVoiceErrorMessage("Connecting to ElevenLabs...")

      // Prepare conversation parameters
      const requestParams: any = {
        voiceId: selectedVoiceId
      }

      // Add script context if available
      if (activeTab) {
        requestParams.scriptId = activeTab
        requestParams.scriptName = fileName || "Unknown script"
        console.log("[CONVERSATION_START] Adding script context:", {
          scriptId: activeTab,
          scriptName: fileName
        })
      }

      console.log("[CONVERSATION_START] Starting conversation with parameters:", requestParams)

      const conversationId = await conversation.startSession({
        agentId: agentId,
        ...requestParams
      })

      console.log("[CONVERSATION_START] ElevenLabs conversation started successfully:", conversationId)
      setApiConfigStatus('valid')
      setVoiceErrorMessage("Connected! You can now speak.")

      // Clear success message after a few seconds
      setTimeout(() => {
        if (apiConfigStatus === 'valid') {
          setVoiceErrorMessage("")
        }
      }, 3000)

    } catch (error) {
      console.error("[CONVERSATION_START] Error starting conversation:", error)

      let errorMessage = "Failed to start conversation"
      let detailedMessage = error instanceof Error ? error.message : String(error)

      // Provide specific error messages based on error type
      if (detailedMessage.includes("agentId") || detailedMessage.includes("agent")) {
        errorMessage = "Invalid agent ID configuration"
      } else if (detailedMessage.includes("auth") || detailedMessage.includes("API key") || detailedMessage.includes("key")) {
        errorMessage = "API authentication failed - missing or invalid API key"
      } else if (detailedMessage.includes("network") || detailedMessage.includes("connect") || detailedMessage.includes("fetch")) {
        errorMessage = "Network connection failed - please check your internet connection"
      } else if (detailedMessage.includes("sign in") || detailedMessage.includes("authentication")) {
        errorMessage = "Authentication required - please sign in"
      } else if (detailedMessage.includes("voice") || detailedMessage.includes("Voice")) {
        errorMessage = "Voice configuration error - please try selecting a different voice"
      } else if (detailedMessage.includes("microphone") || detailedMessage.includes("permission")) {
        errorMessage = "Microphone permission required - please enable microphone access"
      }

      setVoiceErrorMessage(errorMessage)
      setDetailedErrorInfo(detailedMessage)
      setIsListening(false)
      setApiConfigStatus('invalid')
    }
  }

  const handleEndConversation = async () => {
    try {
      setIsListening(false)
      await conversation.endSession()
    } catch (error) {
      console.error("Error ending conversation:", error)
      setVoiceErrorMessage("Failed to end conversation")
      setDetailedErrorInfo(error instanceof Error ? error.message : String(error))
    }
  }

  const toggleMute = async () => {
    try {
      console.log("[AUDIO] Toggling mute. Current state:", { isMuted, voiceStatus })

      // Check if conversation is active
      if (voiceStatus !== 'connected') {
        console.warn("[AUDIO] Cannot change volume - conversation not connected")
        setVoiceErrorMessage("Start a conversation first to control volume")
        return
      }

      const newVolume = isMuted ? 1 : 0
      console.log("[AUDIO] Setting volume to:", newVolume)

      // Try to set volume using the conversation object
      if (conversation.setVolume) {
        conversation.setVolume({ volume: newVolume })
        setIsMuted(!isMuted)
        console.log("[AUDIO] Volume changed successfully to:", newVolume)
      } else {
        console.warn("[AUDIO] setVolume method not available on conversation object")
        setVoiceErrorMessage("Volume control not available")
      }
    } catch (error) {
      console.error("[AUDIO] Error changing volume:", error)
      setVoiceErrorMessage("Failed to change volume")
      setDetailedErrorInfo(error instanceof Error ? error.message : String(error))
    }
  }

  const fetchMostRecentFile = async (): Promise<{id: string, namespace: string, name: string} | null> => {
    if (!userId) return null
    try {
      const filesRef = collection(db, `users/${userId}/files`)
      const q = query(filesRef, orderBy("createdAt", "desc"), limit(1))
      const querySnapshot = await getDocs(q)
      if (!querySnapshot.empty) {
        const fileDoc = querySnapshot.docs[0]
        const fileData = fileDoc.data()
        return {
          id: fileDoc.id,
          namespace: fileData.namespace || fileDoc.id,
          name: fileData.name || "Untitled Document"
        }
      }
      return null
    } catch (err) {
      console.error("Error fetching most recent file:", err)
      return null
    }
  }

  const fetchScriptFiles = async () => {
    if (sessionStatus === "loading") {
      return
    }
    if (!session?.user?.email) {
      setError("Please sign in to access your scripts.")
      setLoading(false)
      return
    }

    try {
      const db = getFirestore()
      const filesRef = collection(db, `users/${session.user.email}/files`)
      const q = query(
        filesRef,
        where("category", "==", "SceneMate"),
        orderBy("name", "asc")
      )
      const querySnapshot = await getDocs(q)

      const files: ScriptFile[] = []
      querySnapshot.forEach((doc) => {
        const fileData = doc.data()
        files.push({
          id: doc.id,
          name: fileData.name || "Untitled Script",
          namespace: fileData.namespace || doc.id
        })
      })
      files.sort((a, b) => a.name.localeCompare(b.name))
      setScriptFiles(files)
      if (!activeTab && files.length > 0) {
        setActiveTab(files[0].id)
      }
      setLoading(false)
    } catch (err) {
      console.error("Error fetching script files:", err)
      setError(`Failed to load scripts: ${err instanceof Error ? err.message : "Unknown error"}`)
      setLoading(false)
    }
  }

  useEffect(() => {
    if (sessionStatus === "authenticated") {
      fetchScriptFiles()
    }
  }, [sessionStatus, userId, activeTab])

  const fetchFileDocumentId = async (namespace: string) => {
    if (!userId) return
    try {
      const filesRef = collection(db, `users/${userId}/files`)
      const q = query(filesRef, where("namespace", "==", namespace), limit(1))
      const querySnapshot = await getDocs(q)
      if (!querySnapshot.empty) {
        const fileDoc = querySnapshot.docs[0]
        setFileDocumentId(fileDoc.id)
        const fileData = fileDoc.data()
        if (fileData.name) {
          setFileName(fileData.name)
        }
      }
    } catch (err) {
      console.error("Error fetching file document ID:", err)
    }
  }

  const fetchScriptContent = async () => {
    if (!activeTab || !userId) {
      setError("No script selected or user not authenticated")
      return
    }

    setIsScriptLoading(true)
    setError(null)

    try {
      // Get file document to retrieve namespace
      const fileDocRef = doc(db, `users/${userId}/files/${activeTab}`)
      const fileDocSnap = await getDoc(fileDocRef)

      if (!fileDocSnap.exists()) {
        setError("Script file not found")
        setIsScriptLoading(false)
        return
      }

      const fileData = fileDocSnap.data()
      const fileNamespace = fileData.namespace || activeTab

      const chunksRef = collection(db, `users/${userId}/byteStoreCollection`)
      const q = query(chunksRef, where("metadata.doc_id", "==", fileNamespace))
      const querySnapshot = await getDocs(q)
      const chunks = querySnapshot.docs.map((d) => d.data())

      if (chunks.length === 0) {
        setError("No content found for this script")
        setIsScriptLoading(false)
        return
      }

      // Sort chunks by position or page_number
      if ("position" in chunks[0]) {
        chunks.sort((a, b) => (a.position || 0) - (b.position || 0))
      } else if ("metadata" in chunks[0] && "page_number" in chunks[0].metadata) {
        chunks.sort((a, b) => (a.metadata.page_number || 0) - (b.metadata.page_number || 0))
      }

      // Determine content field and assemble
      const contentField = "pageContent" in chunks[0] ? "pageContent" : "content"
      const content = chunks.map((chunk) => chunk[contentField] || "").join("\n")
      setScriptContent(content)
      setIsScriptReady(true)
    } catch (err) {
      console.error("Error fetching script content:", err)
      setError("Failed to load script content")
      setIsScriptLoading(false)
    } finally {
      setIsScriptLoading(false)
    }
  }

  const createNewChat = async (fileNamespace?: string, fileDocId?: string) => {
    if (!userId) {
      setError("Please sign in to create a new chat.")
      return null
    }

    try {
      let firstMessageText = "New Rehearsal"
      const chatsRef = collection(db, `users/${userId}/chats`)
      let actualFileDocId: string
      let actualNamespace: string

      if (fileNamespace && fileDocId) {
        actualNamespace = fileNamespace
        actualFileDocId = fileDocId
      } else if (fileDocId) {
        actualNamespace = fileDocId
        actualFileDocId = fileDocId
      } else if (fileNamespace) {
        actualNamespace = fileNamespace
        actualFileDocId = fileNamespace
      } else {
        const recentFile = await fetchMostRecentFile()
        if (recentFile) {
          actualNamespace = recentFile.namespace
          actualFileDocId = recentFile.id
          setFileName(recentFile.name)
        } else {
          setError("Please upload or select a file before creating a chat.")
          return null
        }
      }

      const chatData = {
        createdAt: serverTimestamp(),
        userId: userId,
        firstMessage: firstMessageText,
        lastUpdated: serverTimestamp(),
        fileNamespace: actualNamespace,
        fileDocumentId: actualFileDocId
      }

      const docRef = await addDoc(chatsRef, chatData)
      const newChatId = docRef.id
      setChatId(newChatId)
      setChatMessages([])
      setSelectedFileNamespace(actualNamespace)
      setFileDocumentId(actualFileDocId)
      return newChatId
    } catch (err) {
      if (isMounted.current) {
        setError(
          "Failed to create new chat: " +
            (err instanceof Error ? err.message : "Unknown error")
        )
      }
      return null
    }
  }

  const handleFileUpload = useCallback(
    async (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0]
      if (!file || !userId) {
        setError("No file selected or user not authenticated.")
        return
      }
      try {
        const docId = uuidv4()
        await handleUpload(file, null, userId, docId)
        const newChatId = await createNewChat(docId, docId)

        if (newChatId) {
          setSelectedFileNamespace(docId)
          setFileDocumentId(docId)
          setFileName(file.name)
          setChatId(newChatId)
          const messagesRef = collection(db, `users/${userId}/chats/${newChatId}/messages`)
          const welcomeMessageData = {
            role: "assistant",
            text: "File processed successfully! How can I assist with your script?",
            createdAt: serverTimestamp(),
            fileDocumentId: docId
          }
          const welcomeDocRef = await addDoc(messagesRef, welcomeMessageData)
          const initialMessage: ChatMessage = {
            id: welcomeDocRef.id,
            role: "assistant",
            content: welcomeMessageData.text,
            timestamp: new Date().toISOString(),
            fileDocumentId: docId
          }
          setChatMessages([initialMessage])
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "Upload failed")
      }
    },
    [userId, handleUpload, createNewChat]
  )

  const handleUploadClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  const handleScriptDeleted = useCallback(async () => {
    console.log("Script deleted, updating UI state...");

    // Store the current active tab before refreshing
    const deletedScriptId = activeTab;

    // 1. Clear script selection state immediately
    setActiveTab(null);
    setFileName(null);

    // 2. Reset all script-related state variables
    setScriptContent("");
    setIsScriptLoading(false);
    setIsScriptReady(false);
    setIsFormatting(false);
    setFormattedMarkdown("");

    // 3. Clear any error states
    setError(null);

    // 4. Refresh the script files list to remove deleted script
    try {
      const db = getFirestore();
      const filesRef = collection(db, `users/${session?.user?.email}/files`);
      const q = query(
        filesRef,
        where("category", "==", "SceneMate"),
        orderBy("name", "asc")
      );
      const querySnapshot = await getDocs(q);

      const files: ScriptFile[] = [];
      querySnapshot.forEach((doc) => {
        const fileData = doc.data();
        files.push({
          id: doc.id,
          name: fileData.name || "Untitled Script",
          namespace: fileData.namespace || doc.id
        });
      });

      files.sort((a, b) => a.name.localeCompare(b.name));
      setScriptFiles(files);

      // 5. Handle script selection after refresh
      if (files.length > 0) {
        // Find a script to select (avoid the deleted one)
        const remainingScripts = files.filter(file => file.id !== deletedScriptId);
        if (remainingScripts.length > 0) {
          // Leave selection empty - let user choose
          console.log(`${remainingScripts.length} scripts remaining after deletion`);
          setActiveTab(null);
        } else {
          // All scripts were deleted, leave selection empty
          console.log("No scripts remaining after deletion");
          setActiveTab(null);
        }
      } else {
        // No scripts left, leave selection empty
        console.log("No scripts available after deletion");
        setActiveTab(null);
      }

      setLoading(false);
    } catch (err) {
      console.error("Error refreshing script files after deletion:", err);
      setError(`Failed to refresh scripts: ${err instanceof Error ? err.message : "Unknown error"}`);
      setLoading(false);
    }
  }, [activeTab, session?.user?.email]);

  useEffect(() => {
    if (activeSection === "script" && activeTab) {
      fetchScriptContent()
    }
  }, [activeSection, activeTab])

  // Placeholder for AI formatting (assuming scriptFormatter and markdownFormatterTool exist)
  useEffect(() => {
    if (isScriptReady && scriptContent && !isFormatting) {
      const formatScriptContent = async () => {
        setIsFormatting(true)
        try {
          // Simulate AI formatting (replace with actual implementation if tools are available)
          const formattedScript = {
            metadata: { title: fileName || "Untitled", author: "", characters: [], summary: "" },
            lines: scriptContent.split("\n").map((line, idx) => ({
              lineNumber: idx + 1,
              text: line,
            })),
          }
          setFormattedMarkdown(scriptContent)
        } catch (err) {
          console.error("Error formatting script:", err)
          setFormattedMarkdown(scriptContent)
        } finally {
          setIsFormatting(false)
        }
      }
      formatScriptContent()
    }
  }, [isScriptReady, scriptContent, isFormatting, fileName])

  useEffect(() => {
    return () => {
      isMounted.current = false
    }
  }, [])

  // Cleanup recording when modal closes
  useEffect(() => {
    return () => {
      if (isRecording) {
        stopSelfTakeRecording()
      }
    }
  }, [isOpen])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 dark:bg-black/80 backdrop-blur-sm p-2 sm:p-4 md:p-0">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="relative w-full max-w-full sm:max-w-3xl md:max-w-4xl lg:max-w-6xl h-[90vh] sm:h-[85vh] bg-gradient-to-br from-white via-gray-50 to-gray-100 dark:from-black dark:via-gray-950 dark:to-black rounded-2xl shadow-2xl overflow-hidden border border-gray-300/50 dark:border-gray-700/30 transition-colors duration-300"
      >
        <div className="absolute inset-0 bg-gradient-to-t from-gray-200/10 dark:from-gray-800/10 to-transparent pointer-events-none" />

        <div className="absolute top-4 left-4 flex flex-col space-y-1">
          {sessionStatus === "loading" && (
            <div className="bg-yellow-500/20 text-yellow-300 text-xs px-2 py-1 rounded-md flex items-center">
              <Loader className="w-3 h-3 mr-1 animate-spin" />
              Loading session...
            </div>
          )}
          {sessionStatus === "unauthenticated" && (
            <div className="bg-red-500/20 text-red-300 text-xs px-2 py-1 rounded-md flex items-center">
              <Info className="w-3 h-3 mr-1" />
              Not authenticated
            </div>
          )}
          {apiConfigStatus === 'connecting' && (
            <div className="bg-yellow-500/20 text-yellow-300 text-xs px-2 py-1 rounded-md flex items-center">
              <Loader className="w-3 h-3 mr-1 animate-spin" />
              Connecting to voice API...
            </div>
          )}
          {apiConfigStatus === 'invalid' && (
            <div className="bg-red-500/20 text-red-300 text-xs px-2 py-1 rounded-md flex items-center">
              <Info className="w-3 h-3 mr-1" />
              ElevenLabs API issue
            </div>
          )}
        </div>



        <div className="flex flex-col md:flex-row h-full">
          <AnimatePresence>
            {(isMobileSidebarOpen || isOpen) && (
              <motion.div
                key="sidebar-motion"
                initial={{ x: -300, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                exit={{ x: -300, opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <SideBar
                  activeTab={activeTab}
                  setActiveTab={setActiveTab}
                  scriptFiles={scriptFiles}
                  loading={loading}
                  error={error}
                  setError={setError}
                  isUploading={isUploading}
                  uploadProgress={uploadProgress}
                  uploadStatusText={uploadStatusText}
                  handleUploadClick={handleUploadClick}
                  handleFileUpload={handleFileUpload}
                  fileInputRef={fileInputRef}
                  sessionStatus={sessionStatus}
                  session={session}
                  onClose={onClose}
                  isMobileSidebarOpen={isMobileSidebarOpen}
                  setIsMobileSidebarOpen={setIsMobileSidebarOpen}
                />
              </motion.div>
            )}
          </AnimatePresence>

          <div className="flex-1 overflow-hidden backdrop-blur-sm">
            <div className="h-full flex flex-col">
              {/* Recording Status Bar */}
              {isRecording && (
                <div className="bg-red-600/90 backdrop-blur-sm px-4 py-2 flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-red-300 rounded-full animate-pulse"></div>
                      <span className="text-white font-medium text-sm">Recording Self Take</span>
                    </div>
                    <span className="text-red-100 text-xs">
                      Recording will continue while you navigate between tabs
                    </span>
                  </div>
                  <button
                    onClick={stopSelfTakeRecording}
                    className="bg-white/20 hover:bg-white/30 text-white px-3 py-1 rounded text-sm font-medium transition-colors"
                  >
                    Stop Recording
                  </button>
                </div>
              )}

              <div className="border-b border-gray-200/50 dark:border-white/10 px-4 sm:px-6 py-3 sm:py-4 bg-gray-100/40 dark:bg-black/40 transition-colors duration-300">
                <div className="flex flex-wrap gap-4 sm:space-x-6 pt-3 justify-between items-center">
                  <div className="flex flex-wrap gap-4 sm:space-x-6">
                    {[
                      { name: "rehearsing", label: "Connecting", icon: <Mic className="w-3 h-3 sm:w-4 sm:h-4 mr-1" /> },
                      { name: "chat", label: "Tutor", icon: <MessageSquare className="w-3 h-3 sm:w-4 sm:h-4 mr-1" /> },
                      { name: "script", label: "Script", icon: <Book className="w-3 h-3 sm:w-4 sm:h-4 mr-1" /> },
                      {
                        name: "response",
                        label: "Self Take",
                        icon: <MessageCircle className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />,
                        hasRecordingIndicator: isRecording && activeSection !== "response"
                      },
                      { name: "details", label: "Details", icon: <FileText className="w-3 h-3 sm:w-4 sm:h-4 mr-1" /> },
                    ].map((section) => (
                      <button
                        key={section.name}
                        onClick={() => setActiveSection(section.name)}
                        className={`relative text-xs sm:text-sm font-medium px-2 py-1 border-b-2 transition-all duration-200 flex items-center ${
                          activeSection === section.name
                            ? "border-blue-500 dark:border-gray-500 text-blue-600 dark:text-gray-400"
                            : "border-transparent text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-white hover:border-blue-300 dark:hover:border-white/20"
                        }`}
                      >
                        {section.icon}
                        {section.label}
                        {section.hasRecordingIndicator && (
                          <div className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                        )}
                      </button>
                    ))}
                  </div>

                  <div className="flex items-center space-x-2">
                    {/* Theme Toggle */}
                    <CompactThemeToggle />

                    {/* Desktop Exit Button */}
                    <button
                    onClick={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      onClose()
                    }}
                    className="hidden md:flex p-1.5 rounded-full bg-gray-300/80 dark:bg-gray-600/80 hover:bg-gray-400 dark:hover:bg-gray-500 text-gray-700 dark:text-white transition-all duration-200 shadow-lg shadow-gray-300/20 dark:shadow-gray-500/20 items-center justify-center"
                    aria-label="Close"
                    type="button"
                    title="Close Script Reader"
                  >
                    <X className="w-3.5 h-3.5" />
                  </button>
                </div>
              </div>

              <div className="flex-1 overflow-y-auto p-4 sm:p-6 scrollbar-thin scrollbar-track-gray-200/20 dark:scrollbar-track-white/5 scrollbar-thumb-gray-400/40 dark:scrollbar-thumb-gray-500/20">
                <AnimatePresence mode="wait">
                  <motion.div
                    key={`${activeTab}-${activeSection}`}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.2 }}
                    className="space-y-6 h-full"
                  >
                    {activeSection === "rehearsing" && (
                      <Rehearsals
                        apiConfigStatus={apiConfigStatus}
                        detailedErrorInfo={detailedErrorInfo}
                        isListening={isListening}
                        voiceStatus={voiceStatus}
                        isMuted={isMuted}
                        isSpeaking={isSpeaking}
                        hasPermission={hasPermission}
                        voiceErrorMessage={voiceErrorMessage}
                        toggleMute={toggleMute}
                        handleEndConversation={handleEndConversation}
                        handleStartConversation={handleStartConversation}
                        setVoiceErrorMessage={setVoiceErrorMessage}
                        selectedScriptName={scriptFiles.find(file => file.id === activeTab)?.name}
                        selectedVoiceId={selectedVoiceId}
                        onVoiceSelect={handleVoiceSelect}
                        isUpdatingVoice={isUpdatingVoice}
                      />
                    )}

                    {activeSection === "chat" && <ChatTab chatId={activeTab ?? ''} namespace={namespace} />}

                    {activeSection === "script" && (
                      <ScriptTab
                        scriptContent={scriptContent}
                        isScriptLoading={isScriptLoading || isFormatting}
                        isScriptReady={isScriptReady && !isFormatting}
                        scriptName={fileName}
                        scriptId={activeTab}
                        isListening={isListening}
                        isMuted={isMuted}
                        toggleMute={toggleMute}
                        handleEndConversation={handleEndConversation}
                        onScriptDeleted={handleScriptDeleted}
                      />
                    )}

                    {activeSection === "response" && (
                      <ResponseTab
                        isConnected={voiceStatus === 'connected'}
                        conversationMessages={conversationMessages}
                        onClearMessages={() => setConversationMessages([])}
                        scriptName={fileName}
                        voiceId={selectedVoiceId}
                        sessionDuration={sessionDuration}
                        voiceStatus={voiceStatus}
                        isRecording={isRecording}
                        recordingError={recordingError}
                        recordings={recordings}
                        playingRecording={playingRecording}
                        onStartRecording={startSelfTakeRecording}
                        onStopRecording={stopSelfTakeRecording}
                        onTogglePlayback={toggleRecordingPlayback}
                        onDeleteRecording={deleteRecording}
                      />
                    )}

                    {activeSection === "details" && (
                      <FileDetails
                        activeTab={activeTab}
                        fileName={fileName}
                        namespace={namespace}
                        apiConfigStatus={apiConfigStatus}
                        sessionStatus={sessionStatus}
                        detailedErrorInfo={detailedErrorInfo}
                      />
                    )}
                  </motion.div>
                </AnimatePresence>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  )
}

export { Readermodal }