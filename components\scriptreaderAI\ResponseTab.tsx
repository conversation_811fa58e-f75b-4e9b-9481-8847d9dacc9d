"use client";

import React, { useState, useEffect, useRef } from "react";
import { Download, Clock, MessageCircle, User, Bot, FileText, Mic, Play, Pause, ChevronRight, ChevronLeft, Trash } from "lucide-react";
import jsPDF from "jspdf";
import { useSession } from "next-auth/react";

interface ConversationMessage {
  id: string;
  type: "user" | "assistant";
  content: string;
  timestamp: Date;
}

interface SelfTakeRecording {
  id: string;
  filename: string;
  url: string;
  timestamp: Date;
  duration?: number;
  rehearsalId: string;
}

interface ResponseTabProps {
  isConnected: boolean;
  conversationMessages: ConversationMessage[];
  onClearMessages: () => void;
  scriptName?: string | null;
  voiceId?: string | null;
  sessionDuration?: number;
}

function ResponseTab({
  isConnected,
  conversationMessages,
  onClearMessages,
  scriptName,
  voiceId,
  sessionDuration = 0
}: ResponseTabProps) {
  const { data: session } = useSession();
  const userId = session?.user?.email || "";

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);

  // State for PDF export
  const [isExporting, setIsExporting] = useState(false);

  // State for Self Take panel
  const [isPanelOpen, setIsPanelOpen] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [recordings, setRecordings] = useState<SelfTakeRecording[]>([]);
  const [isLoadingRecordings, setIsLoadingRecordings] = useState(false);
  const [recordingError, setRecordingError] = useState<string | null>(null);
  const [playingRecording, setPlayingRecording] = useState<string | null>(null);
  const [audioElements, setAudioElements] = useState<{ [key: string]: HTMLAudioElement }>({});

  // Auto-scroll to latest message
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [conversationMessages]);

  // Load existing recordings when panel opens
  useEffect(() => {
    if (isPanelOpen && userId) {
      loadRecordings();
    }
  }, [isPanelOpen, userId]);

  // Cleanup audio elements
  useEffect(() => {
    return () => {
      Object.values(audioElements).forEach(audio => {
        audio.pause();
        audio.src = '';
      });
    };
  }, [audioElements]);

  const formatTimestamp = (timestamp: Date) => {
    return timestamp.toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Self Take Recording Functions
  const loadRecordings = async () => {
    if (!userId) return;

    setIsLoadingRecordings(true);
    try {
      const response = await fetch(`/api/selfTakeAudio?userId=${encodeURIComponent(userId)}`);
      if (response.ok) {
        const data = await response.json();
        setRecordings(data.recordings || []);
      } else {
        console.error('Failed to load recordings:', await response.text());
      }
    } catch (error) {
      console.error('Error loading recordings:', error);
      setRecordingError('Failed to load recordings');
    } finally {
      setIsLoadingRecordings(false);
    }
  };

  const startSelfTakeRecording = async () => {
    if (!userId) {
      setRecordingError('Please sign in to record');
      return;
    }

    try {
      setRecordingError(null);
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const recorder = new MediaRecorder(stream);
      mediaRecorderRef.current = recorder;

      const chunks: Blob[] = [];
      recorder.ondataavailable = (e) => chunks.push(e.data);
      recorder.onstop = () => {
        const blob = new Blob(chunks, { type: "audio/mp3" });
        uploadSelfTakeRecording(blob);
        stream.getTracks().forEach((track) => track.stop());
      };

      recorder.start();
      setIsRecording(true);
    } catch (error) {
      setRecordingError("Failed to start recording. Please check your microphone permissions.");
      console.error('Recording error:', error);
    }
  };

  const stopSelfTakeRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  const uploadSelfTakeRecording = async (blob: Blob) => {
    if (!userId) return;

    try {
      const formData = new FormData();
      const rehearsalId = `rehearsal-${Date.now()}`;
      const filename = `selftake-${rehearsalId}.mp3`;

      formData.append("audio", blob, filename);
      formData.append("userId", userId);
      formData.append("rehearsalId", rehearsalId);
      formData.append("scriptName", scriptName || "Unknown Script");

      const response = await fetch("/api/selfTakeAudio", {
        method: "POST",
        body: formData,
      });

      if (response.ok) {
        const data = await response.json();
        const newRecording: SelfTakeRecording = {
          id: data.id,
          filename: data.filename,
          url: data.url,
          timestamp: new Date(data.timestamp),
          rehearsalId: rehearsalId
        };

        setRecordings(prev => [newRecording, ...prev]);
        setRecordingError(null);
      } else {
        const errorData = await response.json();
        setRecordingError(errorData.error || 'Failed to save recording');
      }
    } catch (error) {
      console.error('Upload error:', error);
      setRecordingError('Failed to upload recording');
    }
  };

  const toggleRecordingPlayback = (recording: SelfTakeRecording) => {
    const audioKey = recording.id;

    if (playingRecording === audioKey) {
      // Stop current playback
      if (audioElements[audioKey]) {
        audioElements[audioKey].pause();
      }
      setPlayingRecording(null);
    } else {
      // Stop any currently playing audio
      if (playingRecording && audioElements[playingRecording]) {
        audioElements[playingRecording].pause();
      }

      // Start new playback
      if (!audioElements[audioKey]) {
        const audio = new Audio(recording.url);
        audio.onended = () => setPlayingRecording(null);
        audio.onerror = () => {
          setRecordingError('Failed to play recording');
          setPlayingRecording(null);
        };

        setAudioElements(prev => ({ ...prev, [audioKey]: audio }));
        audio.play().then(() => setPlayingRecording(audioKey));
      } else {
        audioElements[audioKey].currentTime = 0;
        audioElements[audioKey].play().then(() => setPlayingRecording(audioKey));
      }
    }
  };

  const deleteRecording = async (recording: SelfTakeRecording) => {
    if (!userId) return;

    if (!confirm(`Delete recording from ${recording.timestamp.toLocaleString()}?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/selfTakeAudio?id=${recording.id}&userId=${encodeURIComponent(userId)}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        setRecordings(prev => prev.filter(r => r.id !== recording.id));

        // Stop and cleanup audio if it was playing
        if (playingRecording === recording.id) {
          setPlayingRecording(null);
        }
        if (audioElements[recording.id]) {
          audioElements[recording.id].pause();
          audioElements[recording.id].src = '';
          setAudioElements(prev => {
            const newElements = { ...prev };
            delete newElements[recording.id];
            return newElements;
          });
        }
      } else {
        const errorData = await response.json();
        setRecordingError(errorData.error || 'Failed to delete recording');
      }
    } catch (error) {
      console.error('Delete error:', error);
      setRecordingError('Failed to delete recording');
    }
  };

  const exportToPDF = async () => {
    setIsExporting(true);

    try {
      const pdf = new jsPDF();
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();
      const margin = 20;
      const lineHeight = 7;
      let yPosition = margin;

      // Header
      pdf.setFontSize(16);
      pdf.setFont("helvetica", "bold");
      pdf.text("Voice Conversation Transcript", margin, yPosition);
      yPosition += lineHeight * 2;

      // Metadata
      pdf.setFontSize(10);
      pdf.setFont("helvetica", "normal");
      const exportDate = new Date().toLocaleString();
      pdf.text(`Export Date: ${exportDate}`, margin, yPosition);
      yPosition += lineHeight;

      if (scriptName) {
        pdf.text(`Script: ${scriptName}`, margin, yPosition);
        yPosition += lineHeight;
      }

      if (voiceId) {
        pdf.text(`Voice ID: ${voiceId}`, margin, yPosition);
        yPosition += lineHeight;
      }

      pdf.text(`Session Duration: ${formatDuration(sessionDuration)}`, margin, yPosition);
      yPosition += lineHeight;

      pdf.text(`Total Messages: ${conversationMessages.length}`, margin, yPosition);
      yPosition += lineHeight * 2;

      // Messages
      pdf.setFontSize(9);

      for (const message of conversationMessages) {
        // Check if we need a new page
        if (yPosition > pageHeight - margin - 20) {
          pdf.addPage();
          yPosition = margin;
        }

        // Message header
        pdf.setFont("helvetica", "bold");
        const speaker = message.type === "user" ? "User" : "Assistant";
        const timestamp = formatTimestamp(message.timestamp);
        pdf.text(`${speaker} (${timestamp}):`, margin, yPosition);
        yPosition += lineHeight;

        // Message content
        pdf.setFont("helvetica", "normal");
        const lines = pdf.splitTextToSize(message.content, pageWidth - margin * 2);

        for (const line of lines) {
          if (yPosition > pageHeight - margin - 10) {
            pdf.addPage();
            yPosition = margin;
          }
          pdf.text(line, margin + 5, yPosition);
          yPosition += lineHeight;
        }

        yPosition += lineHeight; // Extra space between messages
      }

      // Save the PDF
      const filename = `conversation-transcript-${new Date().toISOString().split('T')[0]}.pdf`;
      pdf.save(filename);

    } catch (error) {
      console.error("Error exporting PDF:", error);
      alert("Failed to export PDF. Please try again.");
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div className="h-full flex bg-gray-900/50 backdrop-blur-sm">
      {/* Main Content */}
      <div className={`flex flex-col transition-all duration-300 ${isPanelOpen ? 'w-2/3' : 'w-full'}`}>
      {/* Header */}
      <div className="p-4 border-b border-white/10 bg-black/20">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <MessageCircle className="w-5 h-5 text-purple-400" />
            <div>
              <h3 className="text-lg font-semibold text-white">Voice Response Log</h3>
              <div className="flex items-center space-x-4 text-sm text-gray-400">
                <span className={`flex items-center space-x-1 ${isConnected ? 'text-green-400' : 'text-red-400'}`}>
                  <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'}`} />
                  <span>{isConnected ? 'Connected' : 'Disconnected'}</span>
                </span>
                <span className="flex items-center space-x-1">
                  <Clock className="w-3 h-3" />
                  <span>{formatDuration(sessionDuration)}</span>
                </span>
                <span>{conversationMessages.length} messages</span>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => setIsPanelOpen(!isPanelOpen)}
              className="flex items-center space-x-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
            >
              <Mic className="w-4 h-4" />
              <span>Self Take</span>
              {isPanelOpen ? <ChevronLeft className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
            </button>

            <button
              onClick={exportToPDF}
              disabled={conversationMessages.length === 0 || isExporting}
              className="flex items-center space-x-2 px-3 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
            >
              <Download className="w-4 h-4" />
              <span>{isExporting ? 'Exporting...' : 'Save as PDF'}</span>
            </button>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {conversationMessages.length === 0 ? (
          <div className="text-center text-gray-400 py-8">
            <MessageCircle className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p className="text-lg mb-2">No conversation recorded yet</p>
            <p className="text-sm">Start a voice conversation to see the transcript here</p>
          </div>
        ) : (
          conversationMessages.map((message) => (
            <div
              key={message.id}
              className={`flex items-start space-x-3 ${
                message.type === "user" ? "justify-start" : "justify-start"
              }`}
            >
              <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                message.type === "user"
                  ? "bg-blue-600"
                  : "bg-purple-600"
              }`}>
                {message.type === "user" ? (
                  <User className="w-4 h-4 text-white" />
                ) : (
                  <Bot className="w-4 h-4 text-white" />
                )}
              </div>

              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2 mb-1">
                  <span className={`text-sm font-medium ${
                    message.type === "user" ? "text-blue-400" : "text-purple-400"
                  }`}>
                    {message.type === "user" ? "You" : "Assistant"}
                  </span>
                  <span className="text-xs text-gray-500">
                    {formatTimestamp(message.timestamp)}
                  </span>
                </div>

                <div className={`p-3 rounded-lg ${
                  message.type === "user"
                    ? "bg-blue-900/30 border border-blue-700/30"
                    : "bg-purple-900/30 border border-purple-700/30"
                } text-white`}>
                  <p className="text-sm leading-relaxed">{message.content}</p>
                </div>
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>
      </div>

      {/* Self Take Panel */}
      {isPanelOpen && (
        <div className="w-1/3 border-l border-white/10 bg-black/20 flex flex-col">
          {/* Panel Header */}
          <div className="p-4 border-b border-white/10">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Mic className="w-5 h-5 text-blue-400" />
                <h3 className="text-lg font-semibold text-white">Self Take Recordings</h3>
              </div>
              <button
                onClick={() => setIsPanelOpen(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <ChevronRight className="w-5 h-5" />
              </button>
            </div>

            {recordingError && (
              <div className="mt-2 p-2 bg-red-900/30 border border-red-700/30 rounded text-red-400 text-sm">
                {recordingError}
              </div>
            )}
          </div>

          {/* Recording Controls */}
          <div className="p-4 border-b border-white/10">
            <button
              onClick={isRecording ? stopSelfTakeRecording : startSelfTakeRecording}
              disabled={!userId}
              className={`w-full flex items-center justify-center space-x-2 px-4 py-3 rounded-lg font-medium transition-colors ${
                isRecording
                  ? 'bg-red-600 hover:bg-red-700 text-white'
                  : 'bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white'
              }`}
            >
              <Mic className={`w-5 h-5 ${isRecording ? 'animate-pulse' : ''}`} />
              <span>{isRecording ? 'Stop Recording' : 'Start Recording'}</span>
            </button>

            {!userId && (
              <p className="mt-2 text-sm text-gray-400 text-center">
                Please sign in to record
              </p>
            )}
          </div>

          {/* Recordings List */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-sm font-medium text-gray-300">Previous Recordings</h4>
              <span className="text-xs text-gray-500">{recordings.length} recordings</span>
            </div>

            {isLoadingRecordings ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-400"></div>
                <span className="ml-2 text-sm text-gray-400">Loading recordings...</span>
              </div>
            ) : recordings.length === 0 ? (
              <div className="text-center py-8">
                <Mic className="w-8 h-8 mx-auto mb-2 text-gray-500 opacity-50" />
                <p className="text-sm text-gray-400">No recordings yet</p>
                <p className="text-xs text-gray-500 mt-1">Start recording to create your first self-take</p>
              </div>
            ) : (
              <div className="space-y-3">
                {recordings.map((recording) => (
                  <div
                    key={recording.id}
                    className="p-3 bg-gray-800/50 rounded-lg border border-gray-700/30 hover:border-gray-600/50 transition-colors"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-white truncate">
                          {recording.filename}
                        </p>
                        <p className="text-xs text-gray-400">
                          {recording.timestamp.toLocaleString()}
                        </p>
                      </div>

                      <div className="flex items-center space-x-2 ml-2">
                        <button
                          onClick={() => toggleRecordingPlayback(recording)}
                          className="p-1.5 bg-blue-600 hover:bg-blue-700 rounded text-white transition-colors"
                        >
                          {playingRecording === recording.id ? (
                            <Pause className="w-3 h-3" />
                          ) : (
                            <Play className="w-3 h-3" />
                          )}
                        </button>

                        <button
                          onClick={() => deleteRecording(recording)}
                          className="p-1.5 bg-red-600 hover:bg-red-700 rounded text-white transition-colors"
                        >
                          <Trash className="w-3 h-3" />
                        </button>
                      </div>
                    </div>

                    {playingRecording === recording.id && (
                      <div className="mt-2 h-1 bg-gray-700 rounded-full overflow-hidden">
                        <div className="h-full bg-blue-400 animate-pulse"></div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

export default ResponseTab;
