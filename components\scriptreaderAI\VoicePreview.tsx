import React, { useState, useRef, useEffect } from 'react';
import { Play, Pause, Loader, Volume2 } from 'lucide-react';
import { VoiceData, generateVoicePreview } from './voiceUtils';

interface VoicePreviewProps {
  voice: VoiceData;
  isSelected: boolean;
  onSelect: (voiceId: string) => void;
  className?: string;
}

const VoicePreview: React.FC<VoicePreviewProps> = ({
  voice,
  isSelected,
  onSelect,
  className = ''
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Cleanup audio URL when component unmounts
  useEffect(() => {
    return () => {
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
    };
  }, [audioUrl]);

  const handlePlayPreview = async () => {
    try {
      setError(null);
      setIsLoading(true);

      // If we already have an audio URL, just play it
      if (audioUrl && audioRef.current) {
        if (isPlaying) {
          audioRef.current.pause();
          setIsPlaying(false);
        } else {
          audioRef.current.play();
          setIsPlaying(true);
        }
        setIsLoading(false);
        return;
      }

      // Generate new preview
      const url = await generateVoicePreview(voice.id, voice.previewText);
      setAudioUrl(url);

      // Create and play audio
      const audio = new Audio(url);
      audioRef.current = audio;

      audio.onended = () => {
        setIsPlaying(false);
      };

      audio.onerror = () => {
        setError('Failed to play audio preview');
        setIsPlaying(false);
      };

      await audio.play();
      setIsPlaying(true);
    } catch (err) {
      console.error('Error playing voice preview:', err);
      setError('Failed to generate voice preview');
    } finally {
      setIsLoading(false);
    }
  };

  const handleStopPreview = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      setIsPlaying(false);
    }
  };

  const handleVoiceSelect = () => {
    // Stop any playing audio when selecting
    handleStopPreview();
    onSelect(voice.id);
  };

  return (
    <div
      className={`
        relative bg-white dark:bg-black/60 backdrop-blur-sm rounded-lg border transition-all duration-200 cursor-pointer
        ${isSelected
          ? 'border-blue-200 dark:border-gray-500 bg-blue-50 dark:bg-gray-500/10 shadow-sm dark:shadow-lg dark:shadow-gray-500/20'
          : 'border-gray-200 dark:border-white/10 hover:border-gray-300 dark:hover:border-gray-400/50 hover:bg-gray-100 dark:hover:bg-gray-500/5'
        }
        ${className}
      `}
      onClick={handleVoiceSelect}
    >
      {/* Selection indicator */}
      {isSelected && (
        <div className="absolute -top-2 -right-2 w-6 h-6 bg-blue-500 dark:bg-gray-500 rounded-full flex items-center justify-center shadow-sm">
          <div className="w-3 h-3 bg-white rounded-full"></div>
        </div>
      )}

      <div className="p-4 space-y-3">
        {/* Avatar placeholder */}
        <div className="w-16 h-16 mx-auto bg-gradient-to-br from-blue-100 to-purple-100 dark:from-purple-500/20 dark:to-blue-500/20 rounded-full flex items-center justify-center border border-gray-200 dark:border-white/10">
          <Volume2 className="w-8 h-8 text-blue-600 dark:text-purple-400" />
        </div>

        {/* Voice name */}
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{voice.name}</h3>
          <p className="text-sm text-gray-500 dark:text-gray-400 capitalize">{voice.gender}</p>
        </div>

        {/* Description */}
        <p className="text-xs text-gray-300 text-center leading-relaxed">
          {voice.description}
        </p>

        {/* Preview button */}
        <div className="flex justify-center">
          <button
            onClick={(e) => {
              e.stopPropagation();
              handlePlayPreview();
            }}
            disabled={isLoading}
            className={`
              flex items-center justify-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors
              ${isLoading
                ? 'bg-gray-700 text-gray-400 cursor-not-allowed'
                : isPlaying
                  ? 'bg-red-600 hover:bg-red-700 text-white'
                  : 'bg-gray-600 hover:bg-gray-700 text-white'
              }
            `}
          >
            {isLoading ? (
              <Loader className="w-4 h-4 animate-spin" />
            ) : isPlaying ? (
              <Pause className="w-4 h-4" />
            ) : (
              <Play className="w-4 h-4" />
            )}
            {isLoading ? 'Loading...' : isPlaying ? 'Stop' : 'Preview'}
          </button>
        </div>

        {/* Error message */}
        {error && (
          <p className="text-xs text-red-400 text-center">{error}</p>
        )}
      </div>
    </div>
  );
};

export default VoicePreview;
