import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-conic":
          "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
      },
      colors: {
        'ike-purple': '#1f2937', // gray-800 equivalent - darker base
        'ike-dark-purple': '#030712', // gray-950 equivalent - nearly black
        'ike-message-bg' : '#111827', // gray-900 equivalent - very dark
        'ike-message-ai' : '#1f2937', // gray-800 equivalent - dark
        'ike-purple_b': '#0f172a' // slate-900 equivalent - very dark
      },
    },
  },
  plugins: [],
};
export default config;