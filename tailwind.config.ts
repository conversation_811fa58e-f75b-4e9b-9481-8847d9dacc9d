import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-conic":
          "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
      },
      colors: {
        'ike-purple': '#374151', // gray-700 equivalent
        'ike-dark-purple': '#111827', // gray-900 equivalent
        'ike-message-bg' : '#1f2937', // gray-800 equivalent
        'ike-message-ai' : '#374151', // gray-700 equivalent
        'ike-purple_b': '#1f2937' // gray-800 equivalent
      },
    },
  },
  plugins: [],
};
export default config;